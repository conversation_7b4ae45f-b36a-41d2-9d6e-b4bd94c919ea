<div class="chat-container">
  <div class="chat-header">
    <h2>Cha<PERSON><PERSON> <PERSON><PERSON>n <PERSON></h2>
  </div>
  <div class="chat-messages">
    <div *ngFor="let message of messages$ | async" class="message-wrapper">
      <div class="message" [ngClass]="{'user-message': message.sender === 'user', 'bot-message': message.sender === 'bot'}">
        <span class="sender-label">{{ message.sender === 'user' ? 'Bạn' : 'Bot' }}:</span>
        <div class="message-content" [innerHTML]="message.text | nl2br"></div>
      </div>
    </div>
    <div *ngIf="isLoading" class="message-wrapper bot-message">
      <div class="message bot-message">
        <span class="sender-label">Bot:</span>
        <div class="message-content">
          <div class="loading-dots">
            <span>.</span><span>.</span><span>.</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="chat-input-area">
    <textarea
      [(ngModel)]="newMessage"
      (keydown.enter)="sendMessage($event)"
      placeholder="Hỏi về sản phẩm..."
      rows="1"
      class="chat-input"
    ></textarea>
    <button (click)="sendMessage()" [disabled]="isLoading" class="send-button">
      Gửi
    </button>
  </div>
</div>
