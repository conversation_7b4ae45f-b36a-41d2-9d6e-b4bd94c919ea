import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

interface ChatResponse {
  answer: string;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private apiUrl = `${environment.apiUrl}/chat`;
  private userId: string;

  constructor(private http: HttpClient) {
    // Khởi tạo hoặc lấy userId từ localStorage
    const storedUserId = localStorage.getItem('chatbotUserId');
    if (storedUserId) {
      this.userId = storedUserId;
    } else {
      this.userId = this.createUuid(); // Tạo UUID ngẫu nhiên
      localStorage.setItem('chatbotUserId', this.userId);
    }
  }

  createUuid(): string {
    // Sử dụng crypto API nếu có sẵn
    if (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID();
    }

    // Phương pháp dự phòng cho các trình duyệt cũ
    // Cần cẩn thận, phương pháp này có thể không an toàn mã hóa
    // và có khả năng trùng lặp cao hơn.
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0,
        v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  sendMessage(query: string): Observable<string> {
    const body = { query: query };
    const headers = new HttpHeaders().set('X-User-ID', this.userId);

    return this.http.post<ChatResponse>(this.apiUrl, body, { headers: headers }).pipe(
      map(response => response.answer)
    );
  }

  // Nếu có phương thức stream, cũng cần cập nhật tương tự
  // Ví dụ:
  // sendStreamMessage(query: string): Observable<string> {
  //   const body = { query: query };
  //   const headers = new HttpHeaders().set('X-User-ID', this.userId);
  //   return this.http.post(this.apiUrl + '/stream', body, { headers: headers, responseType: 'text' });
  // }
}
