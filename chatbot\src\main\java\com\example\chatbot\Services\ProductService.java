package com.example.chatbot.Services;

import java.util.List;
import java.util.Map;

import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.stereotype.Service;

import com.example.chatbot.Entities.Product;
import com.example.chatbot.Repositories.ProductRepository;

import jakarta.annotation.PostConstruct;

@Service
public class ProductService {
    private final ProductRepository productRepository;
    private final VectorStore vectorStore;

    public ProductService(ProductRepository productRepository, PgVectorStore pgVectorStore) {
        this.productRepository = productRepository;
        this.vectorStore = pgVectorStore;
    }

    public Product saveProduct(Product product) {
        Product savedProduct = productRepository.save(product);
        // Tạo Document từ thông tin sản phẩm để lưu vào vector store
        String content = String.format("Tên: %s, <PERSON>h mục: %s, <PERSON><PERSON> tả: %s, Giá: %.0f, Thông số: %s",
                savedProduct.getName(), savedProduct.getCategory(), savedProduct.getDescription(),
                savedProduct.getPrice(), savedProduct.getSpecifications());

        Document document = new Document(content, Map.of(
                "id", savedProduct.getId().toString(),
                "name", savedProduct.getName(),
                "category", savedProduct.getCategory(),
                "price", savedProduct.getPrice(),
                "specifications", savedProduct.getSpecifications()
        ));
        vectorStore.add(List.of(document));
        return savedProduct;
    }

    public List<Product> getAllProducts() {
        return productRepository.findAll();
    }

    public List<Document> searchProductsInVectorStore(String query) {
        return vectorStore.similaritySearch(query);
    }

    @PostConstruct
    public void loadInitialDataToVectorStore() {
        if (productRepository.count() == 0) {
            // Chỉ thêm dữ liệu mẫu nếu DB trống
            List<Product> products = List.of(
                    new Product(null, "Laptop Dell XPS 15", "Máy tính xách tay", "Laptop cao cấp cho lập trình và thiết kế đồ họa. Màn hình OLED sắc nét.", 45000000.0, "CPU: Intel i9, RAM: 32GB, SSD: 1TB, GPU: RTX 4070"),
                    new Product(null, "iPhone 15 Pro", "Điện thoại", "Điện thoại thông minh mạnh mẽ với chip A17 Bionic và hệ thống camera Pro.", 28000000.0, "Màn hình: 6.1 inch OLED, Chip: A17 Bionic, Camera: 48MP, Pin: Lớn"),
                    new Product(null, "Bàn phím cơ Anne Pro 2", "Phụ kiện máy tính", "Bàn phím cơ nhỏ gọn, kết nối không dây và có dây, nhiều tùy chọn switch.", 2500000.0, "Kết nối: Bluetooth 5.0, USB-C; Switch: Gateron Brown; Layout: 60%"),
                    new Product(null, "Màn hình Dell UltraSharp U2723QE", "Màn hình", "Màn hình 4K 27 inch chất lượng cao, độ phủ màu rộng, phù hợp cho đồ họa.", 18500000.0, "Kích thước: 27 inch, Độ phân giải: 4K (3840x2160), Tấm nền: IPS, Cổng kết nối: USB-C, HDMI, DisplayPort"),
                    new Product(null, "Tai nghe Sony WH-1000XM5", "Tai nghe", "Tai nghe chống ồn hàng đầu với chất lượng âm thanh tuyệt vời và thời lượng pin dài.", 7000000.0, "Chống ồn: Chủ động, Thời lượng pin: 30 giờ, Kết nối: Bluetooth 5.2, Codec: LDAC"),
                    new Product(null,"MacBook Pro 16 inch M3 Max","Máy tính xách tay","Dòng laptop chuyên nghiệp mạnh mẽ nhất của Apple, thiết kế cho các tác vụ nặng như chỉnh sửa video 8K, phát triển phần mềm phức tạp và dựng hình 3D.",70000000.0,"CPU: Apple M3 Max (16-core), RAM: 48GB Unified Memory, SSD: 2TB NVMe, GPU: Tích hợp 40-core GPU, Màn hình: 16.2 inch Liquid Retina XDR"),
                    new Product(
            null,
            "PC Gaming Custom High-End",
            "Máy tính để bàn",
            "Bộ PC gaming cấu hình khủng, tối ưu cho trải nghiệm game 4K mượt mà và các tác vụ livestream, render chuyên nghiệp.",
            65000000.0,
            "CPU: Intel Core i9-14900K, RAM: 64GB DDR5, SSD: 2TB NVMe PCIe Gen5, GPU: NVIDIA GeForce RTX 4090, Tản nhiệt: AIO 360mm"
        ),
        new Product(
            null,
            "Laptop ASUS ROG Zephyrus G14",
            "Máy tính xách tay",
            "Laptop gaming nhỏ gọn nhưng mạnh mẽ, kết hợp giữa hiệu năng di động và thiết kế tinh tế, phù hợp cho cả học tập, làm việc và giải trí.",
            32000000.0,
            "CPU: AMD Ryzen 9 7940HS, RAM: 16GB DDR5, SSD: 1TB NVMe PCIe Gen4, GPU: NVIDIA GeForce RTX 4060, Màn hình: 14 inch QHD+ 165Hz"
        ),
        new Product(
            null,
            "Máy tính để bàn HP Pavilion",
            "Máy tính để bàn",
            "Máy tính để bàn đa năng, hiệu quả cho công việc văn phòng, học tập và giải trí gia đình hàng ngày với thiết kế gọn gàng.",
            15000000.0,
            "CPU: Intel Core i5-12400, RAM: 8GB DDR4, SSD: 512GB NVMe, GPU: Intel UHD Graphics 730, Hệ điều hành: Windows 11 Home"
        ),
        new Product(
            null,
            "Laptop Microsoft Surface Laptop 5",
            "Máy tính xách tay",
            "Laptop cao cấp với thiết kế sang trọng, màn hình cảm ứng sắc nét và hiệu năng ổn định, lý tưởng cho người dùng doanh nghiệp và sinh viên.",
            25000000.0,
            "CPU: Intel Core i7-1255U, RAM: 16GB LPDDR5x, SSD: 512GB NVMe, Màn hình: 13.5 inch PixelSense Touch"
        ),
        new Product(
            null,
            "Mac mini M2 Pro",
            "Máy tính để bàn",
            "Thiết bị máy tính để bàn nhỏ gọn nhưng sở hữu sức mạnh vượt trội từ chip M2 Pro, phù hợp cho các nhà phát triển và những người làm sáng tạo nội dung.",
            35000000.0,
            "CPU: Apple M2 Pro (10-core), RAM: 16GB Unified Memory, SSD: 512GB NVMe, GPU: Tích hợp 16-core GPU"
        ),
        new Product(
            null,
            "Laptop Lenovo ThinkPad X1 Carbon Gen 11",
            "Máy tính xách tay",
            "Dòng laptop doanh nhân siêu nhẹ và bền bỉ, mang lại hiệu suất làm việc cao và bảo mật tiên tiến cho các chuyên gia.",
            38000000.0,
            "CPU: Intel Core i7-1360P, RAM: 16GB LPDDR5, SSD: 1TB NVMe PCIe Gen4, Màn hình: 14 inch WUXGA IPS"
        ),
        new Product(
            null,
            "All-in-One PC Dell Inspiron 27",
            "Máy tính để bàn",
            "Máy tính All-in-One tích hợp màn hình lớn, thiết kế gọn gàng, phù hợp cho không gian gia đình hoặc văn phòng nhỏ.",
            22000000.0,
            "CPU: Intel Core i7-1355U, RAM: 16GB DDR4, SSD: 512GB NVMe + 1TB HDD, Màn hình: 27 inch FHD Touch"
        ),
        new Product(
            null,
            "Chromebook Acer Chromebook Spin 714",
            "Máy tính xách tay",
            "Chromebook linh hoạt với thiết kế 2-in-1, thời lượng pin dài, lý tưởng cho học sinh, sinh viên và công việc nhẹ nhàng trên nền tảng Chrome OS.",
            12000000.0,
            "CPU: Intel Core i5-1235U, RAM: 8GB LPDDR4X, SSD: 256GB NVMe, Màn hình: 14 inch FHD Touch, Hệ điều hành: Chrome OS"
        )
            );

            products.forEach(this::saveProduct); // Lưu vào DB và Vector Store
        }
    }
}
