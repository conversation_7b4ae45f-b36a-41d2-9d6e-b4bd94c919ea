spring.application.name=chatbot

# Database PostgreSQL Configuration
spring.datasource.url=**********************************************
spring.datasource.username=postgres
spring.datasource.password=admin123
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.show-sql=true

# Pgvector Store Configuration
spring.ai.vectorstore.pgvector.table-name=product_vectors
spring.ai.vectorstore.pgvector.index-type=HNSW
spring.ai.vectorstore.pgvector.distance-type=COSINE_DISTANCE
spring.ai.vectorstore.pgvector.dimensions=1536

# OpenAI Configuration
spring.ai.openai.api-key=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
spring.ai.openai.chat.options.model=gpt-4o-mini
spring.ai.openai.chat.options.max-completion-tokens=250
spring.ai.openai.embedding.options.model=text-embedding-ada-002

client.url=http://localhost:4200
