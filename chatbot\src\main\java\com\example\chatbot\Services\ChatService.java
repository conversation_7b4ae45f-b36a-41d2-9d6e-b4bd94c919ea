package com.example.chatbot.Services;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

@Service
public class ChatService {
    private final ChatClient chatClient;
    private final VectorStore vectorStore;

    // Lưu trữ lịch sử hội thoại cho mỗi người dùng
    // Key: userId, Value: List of messages (Map<String, String> representing role and content)
    private Map<String, List<Map<String, String>>> conversationHistory = new ConcurrentHashMap<>();
    private static final int MAX_HISTORY_MESSAGES = 10; // Giới hạn 10 tin nhắn trong lịch sử

    public ChatService(ChatClient.Builder chatClientBuilder, VectorStore vectorStore) {
        this.chatClient = chatClientBuilder.build();
        this.vectorStore = vectorStore;
    }

    public String processMessage(String userId, String userQuery) {
        // Lấy lịch sử hội thoại của người dùng
        List<Map<String, String>> history = conversationHistory.computeIfAbsent(userId, k -> new ArrayList<>());

        // Thêm tin nhắn của người dùng vào lịch sử
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", userQuery);
        history.add(userMsg);

        // Tìm kiếm các tài liệu liên quan từ vector store
        List<Document> similarDocuments = vectorStore.similaritySearch(userQuery);

        String context = "";
        if (similarDocuments != null && !similarDocuments.isEmpty()) {
            context = similarDocuments.stream()
                .map(d -> d.getFormattedContent())
                .collect(Collectors.joining("\n"));
        } else {
            // Nếu không tìm thấy tài liệu, có thể trả về thông báo hoặc tiếp tục với ngữ cảnh trống
            // For now, we'll just proceed with an empty context if no documents are found.
        }

        // Chuẩn bị ngữ cảnh cho mô hình AI (bao gồm lịch sử hội thoại và ngữ cảnh sản phẩm)
        List<Map<String, String>> contextMessages = new ArrayList<>();

        // Thêm lịch sử hội thoại vào ngữ cảnh
        // Giới hạn số lượng tin nhắn trong lịch sử để tránh vượt quá token limit
        int startIndex = Math.max(0, history.size() - MAX_HISTORY_MESSAGES);
        for (int i = startIndex; i < history.size(); i++) {
            contextMessages.add(history.get(i));
        }

        // Thêm thông tin sản phẩm vào ngữ cảnh dưới dạng tin nhắn hệ thống hoặc người dùng
        // if (!context.isEmpty()) {
        //     Map<String, String> systemContextMsg = new HashMap<>();
        //     systemContextMsg.put("role", "system");
        //     systemContextMsg.put("content", "Thông tin sản phẩm: " + context);
        //     contextMessages.add(systemContextMsg);
        // }

        // Tạo prompt template
        String template = """
                Bạn là một trợ lý tư vấn sản phẩm thân thiện và chuyên nghiệp.
                Dựa vào thông tin sản phẩm và lịch sử hội thoại, hãy trả lời câu hỏi của người dùng một cách tự nhiên và hữu ích.
                Chỉ sử dụng thông tin được cung cấp, không bịa đặt thêm.
                Câu hỏi của người dùng:
                {query}

                Các sản phẩm phù hợp:
                {context}

                Lịch sử hội thoại:
                {contextMessages}
                """;

        PromptTemplate promptTemplate = new PromptTemplate(template);
        Prompt prompt = promptTemplate.create(java.util.Map.of("query", userQuery, "context", context, "contextMessages", contextMessages)); // Query is now part of contextMessages

        // 5. Gọi LLM và trả về kết quả
        ChatResponse response = chatClient.prompt(prompt).call().chatResponse();
        String aiResponse = "";
        if (response != null && response.getResult() != null && response.getResult().getOutput() != null) {
            aiResponse = response.getResult().getOutput().getText();
        }

        // Thêm phản hồi của AI vào lịch sử
        Map<String, String> aiMsg = new HashMap<>();
        aiMsg.put("role", "assistant");
        aiMsg.put("content", aiResponse);
        history.add(aiMsg);

        return aiResponse;
    }

    // Phương thức để xóa lịch sử hội thoại (ví dụ khi phiên kết thúc)
    public void clearConversationHistory(String userId) {
        conversationHistory.remove(userId);
    }
}
