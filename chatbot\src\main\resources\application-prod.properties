# Production Configuration
spring.application.name=chatbot

# Server Configuration
server.port=8443
server.ssl.enabled=true
server.ssl.key-store-type=PKCS12
server.ssl.key-store=classpath:keystore.p12
server.ssl.key-store-password=changeit
server.ssl.key-alias=tomcat

# Database PostgreSQL Configuration
spring.datasource.url=**********************************************
spring.datasource.username=postgres
spring.datasource.password=admin123
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.show-sql=false

# Pgvector Store Configuration
spring.ai.vectorstore.pgvector.table-name=product_vectors
spring.ai.vectorstore.pgvector.index-type=HNSW
spring.ai.vectorstore.pgvector.distance-type=COSINE_DISTANCE
spring.ai.vectorstore.pgvector.dimensions=1536

# OpenAI Configuration
spring.ai.openai.api-key=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
spring.ai.openai.chat.options.model=gpt-4o-mini
spring.ai.openai.chat.options.max-completion-tokens=250
spring.ai.openai.embedding.options.model=text-embedding-ada-002

# CORS Configuration - Add your production frontend URL
# Replace with your actual frontend domain
client.url=https://your-frontend-domain.com
cors.allowed-origins=https://18.143.66.16,http://18.143.66.16,https://18.143.66.16:4200,http://18.143.66.16:4200
