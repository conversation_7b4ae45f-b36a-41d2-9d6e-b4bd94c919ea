package com.example.chatbot;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.example.chatbot.Services.ProductService;

@Component
public class DataInitializer implements CommandLineRunner {
    private final ProductService productService;

    public DataInitializer(ProductService productService) {
        this.productService = productService;
    }

    @Override
    public void run(String... args) throws Exception {
        productService.loadInitialDataToVectorStore();
    }
}
