# Chatbot Project

This repository contains a chatbot application with a Spring Boot backend and an Angular frontend.

## Project Structure

- `chatbot/`: Contains the Spring Boot backend application.
- `product-chatbot-client/`: Contains the Angular frontend application.

## Getting Started

To set up and run the project, follow the instructions for both the backend and frontend applications.

### Backend (Spring Boot)

Refer to the `chatbot/HELP.md` file for detailed instructions on setting up and running the Spring Boot backend.

### Frontend (Angular)

Refer to the `product-chatbot-client/README.md` file for detailed instructions on setting up and running the Angular frontend.

## Overview

This project aims to provide a conversational interface for interacting with product information. The backend handles data storage and processing, while the frontend provides a user-friendly chat interface.

## Technologies Used

### Backend

- Java
- Spring Boot
- Maven

### Frontend

- Angular
- TypeScript
- HTML
- CSS

## Contributing

Please refer to the individual `HELP.md` and `README.md` files within the `chatbot/` and `product-chatbot-client/` directories for specific contribution guidelines.
