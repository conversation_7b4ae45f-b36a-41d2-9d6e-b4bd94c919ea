package com.example.chatbot;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SpringBootApplication
public class ChatbotApplication {

    @Value("${client.url}")
    private String clientUrl;

    @Value("${cors.allowed-origins:}")
    private String additionalAllowedOrigins;

	public static void main(String[] args) {
		SpringApplication.run(ChatbotApplication.class, args);
	}

	@Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @SuppressWarnings("null")
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                // <PERSON><PERSON><PERSON> danh sách các origins được phép
                List<String> allowedOrigins = new ArrayList<>();
                allowedOrigins.add(clientUrl);
                allowedOrigins.addAll(Arrays.asList(
                    "http://localhost:4200",
                    "https://localhost:4200",
                    "http://127.0.0.1:4200",
                    "https://127.0.0.1:4200",
                    // Production origins
                    "https://************",
                    "http://************",
                    "https://************:4200",
                    "http://************:4200",
                    "https://************:8443",
                    "http://************:8443"
                ));

                // Thêm các origins bổ sung từ configuration nếu có
                if (additionalAllowedOrigins != null && !additionalAllowedOrigins.trim().isEmpty()) {
                    String[] additionalOrigins = additionalAllowedOrigins.split(",");
                    for (String origin : additionalOrigins) {
                        String trimmedOrigin = origin.trim();
                        if (!trimmedOrigin.isEmpty() && !allowedOrigins.contains(trimmedOrigin)) {
                            allowedOrigins.add(trimmedOrigin);
                        }
                    }
                }

                registry.addMapping("/api/**")
                        .allowedOrigins(allowedOrigins.toArray(new String[0]))
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")
                        .allowedHeaders("*")
                        .allowCredentials(false)
                        .maxAge(3600); // Cache preflight response for 1 hour
            }
        };
    }

}
