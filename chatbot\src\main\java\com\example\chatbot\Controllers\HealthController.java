package com.example.chatbot.Controllers;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class HealthController {

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Chatbot API is running");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    @GetMapping("/cors-test")
    public Map<String, Object> corsTest() {
        Map<String, Object> response = new HashMap<>();
        response.put("cors", "working");
        response.put("message", "CORS is configured correctly");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
