package com.example.chatbot.Controllers;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader; // Import RequestHeader
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.example.chatbot.Services.ChatService;

@RestController
@RequestMapping("/api/chat")
public class ChatController {
    private final ChatService chatService;

    public ChatController(ChatService chatService) {
        this.chatService = chatService;
    }

    public record ChatRequest(String query) {}
    public record ChatResponse(String answer) {}

    @PostMapping
    public ChatResponse chat(@RequestHeader("X-User-ID") String userId, @RequestBody ChatRequest request) {
        String answer = chatService.processMessage(userId, request.query());
        return new ChatResponse(answer);
    }
}
