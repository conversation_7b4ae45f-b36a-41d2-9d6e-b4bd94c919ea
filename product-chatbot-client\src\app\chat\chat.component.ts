import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ChatService } from './chat.service';
import { BehaviorSubject } from 'rxjs';
import { Nl2brPipe } from "./nl2br.pipe";

interface Message {
  text: string;
  sender: 'user' | 'bot';
}

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    Nl2brPipe
]
})
export class ChatComponent implements OnInit {
  @ViewChild('chatMessages') private chatMessagesRef!: ElementRef;

  _messages = new BehaviorSubject<Message[]>([]);
  messages$ = this._messages.asObservable();
  newMessage: string = '';
  isLoading: boolean = false;

  constructor(private chatService: ChatService) { }

  ngOnInit(): void {
    this.addItem({ text: "Chào bạn! Tôi là chatbot tư vấn sản phẩm. Bạn muốn tìm hiểu về sản phẩm nào?", sender: 'bot' });
  }

  addItem(newItem: Message) {
    const currentItems = this._messages.getValue(); // Get the current array value
    const updatedItems = [...currentItems, newItem]; // Create a new array with the added item
    this._messages.next(updatedItems); // Emit the new array
  }

  scrollToBottom(): void {
    try {
      this.chatMessagesRef.nativeElement.scrollTop = this.chatMessagesRef.nativeElement.scrollHeight;
    } catch (err) { }
  }

  trackByMessages(index: number, message: Message): number {
    return index; // Use index as a unique identifier for messages
  }

  sendMessage(event?: Event): void {
    if (event) {
      event.preventDefault(); // Prevent the default action (e.g., newline in textarea)
    }

    if (this.newMessage.trim() === '') {
      return;
    }

    // Add user message to chat
    this.addItem({ text: this.newMessage, sender: 'user' });
    const userQuestion = this.newMessage;
    this.newMessage = ''; // Clear input field
    this.isLoading = true; // Show loading indicator

    // Send message to backend
    this.chatService.sendMessage(userQuestion).subscribe({
      next: (response: string) => {
        // Add bot response to chat
        this.isLoading = false;
        this.addItem({ text: response, sender: 'bot' });
        this.scrollToBottom(); // Scroll to bottom after new message
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Lỗi khi gửi tin nhắn:', error);
        this.addItem({ text: 'Rất tiếc, đã có lỗi xảy ra. Vui lòng thử lại sau.', sender: 'bot' });
        this.scrollToBottom(); // Scroll to bottom even on error
      }
    });
  }
}
